import { useState, useEffect } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { convertAnonymousUser, transferCertificateToExistingAccount, checkEmailExists } from '../../utils/accountConversion';
import { useCertificate } from '../../contexts/CertificateContext';
import { supabase } from '../../lib/supabase';

interface AccountConversionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  userEmail: string;
}

type AuthMode = 'checking' | 'login' | 'register';

export const AccountConversionModal = ({
  isOpen,
  onClose,
  onSuccess,
  userEmail
}: AccountConversionModalProps) => {
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [authMode, setAuthMode] = useState<AuthMode>('checking');
  const [loginPassword, setLoginPassword] = useState('');
  const [isTransferringFiles, setIsTransferringFiles] = useState(false);

  const { signIn } = useAuth();
  const { activeCertificateId } = useCertificate();

  // Check email registration status when modal opens
  useEffect(() => {
    const checkEmailStatus = async () => {
      if (!isOpen || !userEmail) return;

      setAuthMode('checking');
      setError(null);

      try {
        const emailExists = await checkEmailExists(userEmail);

        if (emailExists) {
          setAuthMode('login');
        } else {
          setAuthMode('register');
        }
      } catch (err) {
        console.error('Error checking email existence:', err);
        // If we can't check, proceed with normal flow
        setAuthMode('register'); // Default to registration on error
      }
    };

    checkEmailStatus();
  }, [isOpen, userEmail]);

  const handleRegistration = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate form
    if (!password || !confirmPassword) {
      setError('Bitte füllen Sie alle Felder aus.');
      return;
    }

    if (password !== confirmPassword) {
      setError('Die Passwörter stimmen nicht überein.');
      return;
    }

    if (password.length < 6) {
      setError('Das Passwort muss mindestens 6 Zeichen lang sein.');
      return;
    }

    try {
      setLoading(true);
      setIsTransferringFiles(true);

      // We'll check certificate ownership after conversion to ensure it's correct

      // Since we already checked for email conflicts upfront, we can proceed directly with conversion
      // Use the enhanced conversion function that includes file transfer
      const result = await convertAnonymousUser(userEmail, password, activeCertificateId || undefined);

      if (!result.success) {
        setError(result.error || 'Fehler beim Erstellen des Kontos.');
        return;
      }

      // Log file transfer results if available
      if (result.fileTransferResult) {
        const { transferredFiles, failedFiles } = result.fileTransferResult;
        console.log(`📁 File transfer results: ${transferredFiles || 0} files transferred`);
        if (failedFiles && failedFiles.length > 0) {
          console.warn(`⚠️ Some files failed to transfer: ${failedFiles.join(', ')}`);
        }
      }

      // After successful conversion, get the updated user
      const { data: updatedUser } = await supabase.auth.getUser();
      const newUserId = updatedUser.user?.id;

      // Always ensure certificate ownership is correct after conversion
      // This handles edge cases where the user ID might change or certificate ownership gets confused
      if (activeCertificateId && newUserId) {
        // Check if the certificate is currently owned by the correct user
        const { data: certificateData, error: fetchError } = await supabase
          .from('energieausweise')
          .select('user_id')
          .eq('id', activeCertificateId)
          .single();

        if (fetchError) {
          console.error('Error fetching certificate ownership:', fetchError);
          setError('Fehler beim Überprüfen der Zertifikatsdaten.');
          return;
        }

        // If the certificate is not owned by the current user, transfer it
        if (certificateData.user_id !== newUserId) {
          console.log(`🔄 Certificate ownership mismatch detected during conversion: ${certificateData.user_id} !== ${newUserId}`);
          const transferResult = await transferCertificateToExistingAccount(
            activeCertificateId,
            newUserId
          );

          if (!transferResult.success) {
            console.error('❌ Certificate transfer failed during conversion:', transferResult.error);
            setError(transferResult.error || 'Fehler beim Übertragen der Zertifikatsdaten.');
            return;
          }
          console.log('✅ Certificate transfer completed successfully during conversion');
        } else {
          console.log('✅ Certificate already owned by correct user after conversion');
        }
      }

      // Success
      onSuccess();
    } catch (err: any) {
      console.error('Account conversion error:', err);
      setError('Fehler beim Erstellen des Kontos. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
      setIsTransferringFiles(false);
    }
  };

  // Handle login for existing users
  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    if (!loginPassword) {
      setError('Bitte geben Sie Ihr Passwort ein.');
      return;
    }

    try {
      setLoading(true);
      setIsTransferringFiles(true);

      // Sign in with existing account
      const { data, error } = await signIn(userEmail, loginPassword);

      if (error || !data?.user) {
        setError('Ungültige Anmeldedaten. Bitte überprüfen Sie Ihr Passwort.');
        return;
      }

      // Transfer certificate data to the existing account
      if (activeCertificateId) {
        console.log(`🔄 Starting certificate transfer for login: ${activeCertificateId} -> ${data.user.id}`);
        const transferResult = await transferCertificateToExistingAccount(
          activeCertificateId,
          data.user.id
        );

        if (!transferResult.success) {
          console.error('❌ Certificate transfer failed during login:', transferResult.error);
          setError(transferResult.error || 'Fehler beim Übertragen der Zertifikatsdaten.');
          return;
        }
        console.log('✅ Certificate transfer completed successfully during login');
      }

      console.log('Login successful');
      onSuccess();
    } catch (error) {
      console.error('Login error:', error);
      setError('Ein unerwarteter Fehler ist aufgetreten. Bitte versuchen Sie es erneut.');
    } finally {
      setLoading(false);
      setIsTransferringFiles(false);
    }
  };





  // Reset all state when modal closes
  const handleClose = () => {
    setPassword('');
    setConfirmPassword('');
    setError(null);
    setAuthMode('checking');
    setLoginPassword('');
    setIsTransferringFiles(false);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-md w-full p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-gray-800">
            {authMode === 'checking' ? 'E-Mail wird überprüft...' :
             authMode === 'login' ? 'Mit bestehendem Konto anmelden' : 'Konto erstellen'}
          </h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
            disabled={authMode === 'checking'}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {authMode === 'checking' && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
            <span className="ml-3 text-gray-600">E-Mail-Adresse wird überprüft...</span>
          </div>
        )}

        {authMode === 'register' && (
          <>
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Erstellen Sie ein Passwort für Ihr Konto mit der E-Mail-Adresse{' '}
                <strong>{userEmail}</strong>. So können Sie Ihre Energieausweise später verwalten.
              </p>
            </div>

            <form onSubmit={handleRegistration} className="space-y-4">
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort
                </label>
                <div className="relative">
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Mindestens 6 Zeichen"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort bestätigen
                </label>
                <div className="relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                    placeholder="Passwort wiederholen"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                    ) : (
                      <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                      </svg>
                    )}
                  </button>
                </div>
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}

              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors disabled:bg-green-400 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (isTransferringFiles ? 'Konto wird erstellt und Dateien übertragen...' : 'Konto wird erstellt...') : 'Konto erstellen'}
                </button>
              </div>
            </form>

            <div className="mt-4 text-xs text-gray-500">
              <p>
                Durch das Erstellen eines Kontos können Sie Ihre Energieausweise verwalten,
                den Status einsehen und bei Bedarf erneut herunterladen.
              </p>
            </div>
          </>
        )}

        {authMode === 'login' && (
          <>
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm text-blue-700">
                Die E-Mail-Adresse <strong>{userEmail}</strong> ist bereits registriert.
                Melden Sie sich mit Ihrem bestehenden Konto an. Ihre Zertifikatsdaten werden automatisch übertragen.
              </p>
            </div>

            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label htmlFor="loginPassword" className="block text-sm font-medium text-gray-700 mb-1">
                  Passwort
                </label>
                <input
                  id="loginPassword"
                  name="loginPassword"
                  type="password"
                  autoComplete="current-password"
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Ihr Passwort"
                  value={loginPassword}
                  onChange={(e) => setLoginPassword(e.target.value)}
                />
              </div>

              {error && (
                <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded">
                  {error}
                </div>
              )}

              <div className="flex justify-end pt-4">
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors disabled:bg-blue-400 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (isTransferringFiles ? 'Anmeldung läuft und Daten werden übertragen...' : 'Anmeldung läuft...') : 'Anmelden'}
                </button>
              </div>
            </form>
          </>
        )}
      </div>
    </div>
  );
};
